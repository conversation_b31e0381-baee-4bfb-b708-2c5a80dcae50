
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torcheval.metrics import R2Score

from sklearn.metrics import root_mean_squared_error, mean_absolute_error, r2_score

from sklearn.model_selection import KFold


class EarlyStopping:
    def __init__(self, model_path, patience=10, min_delta=0, mode='max', start_from_epoch=0):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.start_from_epoch = start_from_epoch
        
        self.model_path = model_path
        self.counter = 0
        self.best_metric = float('inf')
        self.best_epoch = 0
    
    def __call__(self, updated_value, epoch, model):
        if self.mode == 'max':
            updated_value = -updated_value
        if updated_value < self.best_metric:
            self.best_metric = updated_value
            self.best_epoch = epoch
            self.counter = 0
            torch.save(model.state_dict(), self.model_path)
        elif updated_value >= (self.best_metric - self.min_delta):
            if epoch >= self.start_from_epoch:
                self.counter += 1
            if self.counter >= self.patience:
                return True
        return False


class GYX_Dataset(Dataset):
    def __init__(self, X, y):
        self.X = X
        self.y = y.reshape(-1, 1)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
    

class BPNN(nn.Module):
    def __init__(self, input_dim, hidden_dim):
        super(BPNN, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.relu = nn.ReLU()
        self.fc3 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc2 = nn.Linear(hidden_dim // 2, 1)

    def forward(self, inputs):
        out = self.fc1(inputs)
        out = self.relu(out)
        out = self.fc3(out)
        out = self.relu(out)
        out = self.fc2(out)
        return out


def load_dataset(endpoint):
    DATA_DIR = "./data/"
    data_filename = f"imputed_{endpoint}.csv"
    df = pd.read_csv(DATA_DIR + data_filename)

    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    target_label_map = {'HAA': 'HAAs',
                        'HAN': 'HANs',
                        'THM': 'T-THMs'}
    X = df[predictors].values
    y = df[target_label_map[endpoint]].values 
    return X, y


def train(model, dataloader, criterion, optimizer, device):
    model.train()
    total_mse_loss = 0.0
    total_examples = 0
    r2_metric = R2Score()

    for inputs, targets in dataloader:
        inputs = inputs.float().to(device)
        targets = targets.float().to(device)
        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()

        total_mse_loss += loss.item()
        r2_metric.update(outputs, targets)
        total_examples += targets.size(0)
    
    avg_mse_loss = total_mse_loss / total_examples
    r2_score = r2_metric.compute()

    return total_mse_loss, avg_mse_loss, r2_score.item()

    
def eval(model, dataloader, criterion, device):
    model.eval()
    total_mse_loss = 0.0
    total_examples = 0
    r2_metric = R2Score()
    true_targets = []
    pred_targets = []

    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs = inputs.float().to(device)
            targets = targets.float().to(device)

            outputs = model(inputs)
            loss = criterion(outputs, targets)
            total_mse_loss += loss.item()

            r2_metric.update(outputs, targets)
            total_examples += targets.size(0)
            
            true_targets.extend(targets.cpu().numpy())
            pred_targets.extend(outputs.cpu().numpy())

    avg_mse_loss = total_mse_loss / total_examples
    r2_score = r2_metric.compute()

    return total_mse_loss, avg_mse_loss, r2_score.item(), true_targets, pred_targets

def get_centers_and_sigmas(X, num_rbf_neurons, kmeans_seed):
    kmeans = KMeans(n_clusters=num_rbf_neurons, random_state=kmeans_seed).fit(X)

    centroids = kmeans.cluster_centers_

    sigma_squares = np.ones(num_rbf_neurons) * -1
    labels = kmeans.labels_
    for idx, centroid in enumerate(centroids):
        cluster_mask = labels == idx
        count = cluster_mask.sum()
        sigma2 = np.sum((X[cluster_mask] - centroid) ** 2) / count
        sigma2 = 1 if sigma2 == 0 else sigma2
        sigma_squares[idx] = sigma2

    return centroids, sigma_squares

def transform_into_features(X, centroids, sigma_squares):
    generate_rbf = lambda x, c, s: np.exp(-1 * np.sum((x - c) ** 2 / (2 * s), axis=1))
    return np.array([generate_rbf(X, centroid, sigma_square)
                     for centroid, sigma_square in zip(centroids, sigma_squares)]).T

def main(endpoint, random_seed, num_neurons, kmeans_seed):

    reg = 0.01
    emb_dim = 64

    torch.manual_seed(random_seed)

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(device)

    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    X, y = load_dataset(endpoint)

    X_train_all_o, X_test, y_train_all, y_test = train_test_split(X, y, test_size=0.2, random_state=random_seed)
    X_train, X_val, y_train, y_val = train_test_split(X_train_all_o, y_train_all, test_size=0.2, random_state=random_seed)

    # Normalize Data
    scaler = StandardScaler().fit(X_train_all_o)
    X_train_all_o = scaler.transform(X_train_all_o)
    X_train = scaler.transform(X_train)
    X_val = scaler.transform(X_val)
    X_test = scaler.transform(X_test)

    centroids, sigma_squares = get_centers_and_sigmas(X_train, num_neurons, kmeans_seed)
    np.save(f"./RBF_checkpoints/{endpoint}/{endpoint}_RBF_seed{random_seed}_centroids.npy", centroids)
    np.save(f"./RBF_checkpoints/{endpoint}/{endpoint}_RBF_seed{random_seed}_sigma_squares.npy", sigma_squares)

    X_train_f = transform_into_features(X_train, centroids, sigma_squares)
    X_val_f = transform_into_features(X_val, centroids, sigma_squares)
    X_test_f = transform_into_features(X_test, centroids, sigma_squares)
    
    X_train_all_f = transform_into_features(X_train_all_o, centroids, sigma_squares)

    X_train = np.concatenate([X_train, X_train_f], axis=1)
    X_val = np.concatenate([X_val, X_val_f], axis=1)
    X_test = np.concatenate([X_test, X_test_f], axis=1)
    X_train_all = np.concatenate([X_train_all_o, X_train_all_f], axis=1)


    train_dataset = GYX_Dataset(X_train, y_train)
    val_dataset = GYX_Dataset(X_val, y_val)
    test_dataset = GYX_Dataset(X_test, y_test)

    eval_train_val_dataset = GYX_Dataset(X_train_all, y_train_all)

    val_size = X_val.shape[0]
    test_size = X_test.shape[0]
    train_dataloader = DataLoader(train_dataset, batch_size=128, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=val_size, shuffle=False)
    test_dataloader = DataLoader(test_dataset, batch_size=test_size, shuffle=False)
    eval_train_val_dataloader = DataLoader(eval_train_val_dataset, batch_size=64, shuffle=False)

    # Build Model
    input_dim = num_neurons + len(predictors)
    hidden_dim = emb_dim
    model = BPNN(input_dim=input_dim, hidden_dim=hidden_dim).to(device)

    # Train settings
    lr = 1e-3
    num_epochs = 20000
    criterion = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=reg)
    model_path = f"./RBF_checkpoints/{endpoint}/best_{hidden_dim}dim_{endpoint}_seed{random_seed}_n{num_neurons}_kseed_{kmeans_seed}.pt"
    early_stopper = EarlyStopping(model_path=model_path,
                                  patience=500, mode='max', start_from_epoch=1000)

    for epoch in range(num_epochs):
        train_total_mse_loss, train_avg_mse_loss, train_r2 = train(model, train_dataloader, criterion, optimizer, device)
        val_total_mse_loss, val_avg_mse_loss, val_r2, val_targets, val_preds = eval(model, val_dataloader, criterion, device=device)
        

        if (epoch + 1) % 1000 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}")
            print(f"\tTrain:")
            print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")
            print(f"\tValidation")
            print(f"\t\t val_total_loss: {val_total_mse_loss:.6f}, val_avg_loss: {val_avg_mse_loss:.6f}, val_r2: {val_r2:.6f}")
        
        if early_stopper(val_r2, epoch+1, model):
            model.load_state_dict(torch.load(model_path))
            print(f"Improvement is not observed for the past {early_stopper.patience} epochs. Early Stopping...")
            print(f"Loading Best Model Checkpoint from epoch {early_stopper.best_epoch} with validation r2 {-early_stopper.best_metric:.4f}")
            break

    model.load_state_dict(torch.load(model_path))

    print("Evaluation")   
    train_total_mse_loss, train_avg_mse_loss, train_r2, train_targets, train_preds = eval(model, eval_train_val_dataloader, criterion, device=device)
    print(f"\tTrain")
    print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")


    test_total_mse_loss, test_avg_mse_loss, test_r2, test_targets, test_preds = eval(model, test_dataloader, criterion, device=device)
    print(f"\tTest")
    print(f"\t\t test_total_loss: {test_total_mse_loss:.6f}, test_avg_loss: {test_avg_mse_loss:.6f}, test_r2: {test_r2:.6f}")

    torch.save(model, f"./results/models/{endpoint}_RBF_seed{random_seed}_best.pt")

    scores = cross_validation(X_train_all_o, y_train_all, endpoint, num_neurons, kmeans_seed, cv=10)
    q2 = np.mean(scores)

    result_dict = {'endpoint': endpoint,
                   'seed': random_seed,
                   'num_neurons': num_neurons,
                   'kmeans_seed': kmeans_seed,
                   'l2_reg': reg,
                   'train_rmse': root_mean_squared_error(train_targets, train_preds),
                   'train_r2': train_r2,
                   'ext_rmse': root_mean_squared_error(test_targets, test_preds),
                   'ext_r2': test_r2,
                   'train_q2': q2,
                   'hidden_dim': hidden_dim,
                   'best_epoch': early_stopper.best_epoch}

    train_pred_dict = {'train_true': np.array(train_targets).squeeze(), 'train_pred': np.array(train_preds).squeeze()}
    pred_df = pd.DataFrame(train_pred_dict)
    pred_df.to_csv(f"./results/predictions/{endpoint}_RBF_train_predictions.csv", index=False)

    ext_pred_dict = {'ext_true': np.array(test_targets).squeeze(), 'ext_pred': np.array(test_preds).squeeze()}
    pred_df = pd.DataFrame(ext_pred_dict)
    pred_df.to_csv(f"./results/predictions/{endpoint}_RBF_ext_predictions.csv", index=False)

    result_df = pd.DataFrame([result_dict])
    result_df.to_csv(f"./results/scores/{endpoint}_RBF_results.csv", index=False)

    return result_dict

def cross_validation(X, y, endpoint, num_neurons, kmeans_seed, cv):
    
    reg = 0.01
    emb_dim = 64

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    validation_scores = []

    kf = KFold(n_splits=cv)
    for i, (train_idx, test_idx) in enumerate(kf.split(X)):
        curr_X_train, curr_X_val, curr_y_train, curr_y_val = X[train_idx], X[test_idx], y[train_idx], y[test_idx]
        val_size = curr_X_val.shape[0]

        centroids, sigma_squares = get_centers_and_sigmas(curr_X_train, num_neurons, kmeans_seed)
        curr_X_train_f = transform_into_features(curr_X_train, centroids, sigma_squares)
        curr_X_val_f = transform_into_features(curr_X_val, centroids, sigma_squares)

        curr_X_train = np.concatenate([curr_X_train, curr_X_train_f], axis=1)
        curr_X_val = np.concatenate([curr_X_val, curr_X_val_f], axis=1)

        train_dataset = GYX_Dataset(curr_X_train, curr_y_train)
        val_dataset = GYX_Dataset(curr_X_val, curr_y_val)

        train_dataloader = DataLoader(train_dataset, batch_size=128, shuffle=True)
        val_dataloader = DataLoader(val_dataset, batch_size=val_size, shuffle=False)

        # Build Model
        input_dim = num_neurons + len(predictors)
        hidden_dim = emb_dim
        model = BPNN(input_dim=input_dim, hidden_dim=hidden_dim).to(device)

        # Train settings
        lr = 1e-3
        num_epochs = 10000
        criterion = nn.MSELoss()
        optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=reg)
        model_path = f"./RBF_checkpoints/{endpoint}/best_{hidden_dim}dim_{endpoint}_cv_fold{i}.pt"
        early_stopper = EarlyStopping(model_path=model_path,
                                        patience=500, mode='max', start_from_epoch=1000)

        for epoch in range(num_epochs):
            train_total_mse_loss, train_avg_mse_loss, train_r2 = train(model, train_dataloader, criterion, optimizer, device)
            val_total_mse_loss, val_avg_mse_loss, val_r2, val_targets, val_preds = eval(model, val_dataloader, criterion, device=device)
            

            if (epoch + 1) % 1000 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}")
                print(f"\tTrain:")
                print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")
                print(f"\tValidation")
                print(f"\t\t val_total_loss: {val_total_mse_loss:.6f}, val_avg_loss: {val_avg_mse_loss:.6f}, val_r2: {val_r2:.6f}")
            
            if early_stopper(val_r2, epoch+1, model):
                model.load_state_dict(torch.load(model_path))
                print(f"Improvement is not observed for the past {early_stopper.patience} epochs. Early Stopping...")
                print(f"Loading Best Model Checkpoint from epoch {early_stopper.best_epoch} with validation r2 {-early_stopper.best_metric:.4f}")
                break

        model.load_state_dict(torch.load(model_path))

        print("Evaluation")   
        test_total_mse_loss, test_avg_mse_loss, test_r2, test_targets, test_preds = eval(model, val_dataloader, criterion, device=device)
        print(f"\tTest")
        print(f"\t\t test_total_loss: {test_total_mse_loss:.6f}, test_avg_loss: {test_avg_mse_loss:.6f}, test_r2: {test_r2:.6f}")

        validation_scores.append(test_r2)

    return validation_scores

if __name__ == '__main__':


    endpoint = 'HAN'    # or 'HAN' or 'THM'

    random_seed = 2 if endpoint == 'HAA' else 60 if endpoint == 'HAN' else 0
    num_neurons = 10 if endpoint == 'HAA' else 11 if endpoint == 'HAN' else 10
    kmeans_seed = 3823357485 if endpoint == 'HAA' else 1966256721 if endpoint == 'HAN' else 3222507488

    main(endpoint, random_seed, num_neurons, kmeans_seed)

