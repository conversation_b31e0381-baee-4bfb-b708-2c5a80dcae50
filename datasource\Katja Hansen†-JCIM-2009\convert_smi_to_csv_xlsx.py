#!/usr/bin/env python3
"""
SMI文件转换工具
将SMI格式文件转换为CSV和XLSX格式
"""

import pandas as pd
import os
import sys
from pathlib import Path

def convert_smi_to_formats(smi_file_path, output_dir=None):
    """
    将SMI文件转换为CSV和XLSX格式
    
    Args:
        smi_file_path (str): SMI文件路径
        output_dir (str, optional): 输出目录，默认为SMI文件所在目录
    
    Returns:
        tuple: (csv_file_path, xlsx_file_path)
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(smi_file_path):
        raise FileNotFoundError(f"SMI文件不存在: {smi_file_path}")
    
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.dirname(smi_file_path)
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取文件名（不含扩展名）
    base_name = Path(smi_file_path).stem
    
    # 读取SMI文件
    print(f"正在读取SMI文件: {smi_file_path}")
    
    try:
        # 读取SMI文件，使用制表符分隔
        df = pd.read_csv(smi_file_path, 
                        sep='\t', 
                        header=None, 
                        names=['SMILES', 'CAS_Number', 'Label'],
                        encoding='utf-8')
        
        print(f"成功读取 {len(df)} 行数据")
        
        # 显示数据预览
        print("\n数据预览:")
        print(df.head())
        print(f"\n数据形状: {df.shape}")
        print(f"\n各列数据类型:")
        print(df.dtypes)
        
        # 检查是否有缺失值
        missing_values = df.isnull().sum()
        if missing_values.any():
            print(f"\n缺失值统计:")
            print(missing_values)
        
        # 保存为CSV格式
        csv_file_path = os.path.join(output_dir, f"{base_name}.csv")
        df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
        print(f"\n已保存CSV文件: {csv_file_path}")
        
        # 保存为XLSX格式
        xlsx_file_path = os.path.join(output_dir, f"{base_name}.xlsx")
        df.to_excel(xlsx_file_path, index=False, engine='openpyxl')
        print(f"已保存XLSX文件: {xlsx_file_path}")
        
        return csv_file_path, xlsx_file_path
        
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        raise

def main():
    """主函数"""
    
    # 默认的SMI文件路径
    default_smi_file = r"D:\BXL\wanhua\遗传毒性\datasource\Katja Hansen†-JCIM-2009\smiles_cas_N6512.smi"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        smi_file_path = sys.argv[1]
    else:
        smi_file_path = default_smi_file
    
    # 检查输出目录参数
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        print("=" * 60)
        print("SMI文件转换工具")
        print("=" * 60)
        
        # 执行转换
        csv_path, xlsx_path = convert_smi_to_formats(smi_file_path, output_dir)
        
        print("\n" + "=" * 60)
        print("转换完成!")
        print(f"CSV文件: {csv_path}")
        print(f"XLSX文件: {xlsx_path}")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
