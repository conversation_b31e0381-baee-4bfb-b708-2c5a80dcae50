#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import dgl
from dgl import DGLGraph
from rdkit import Chem
from tqdm import tqdm
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import pickle
# dgllife 相关导入
from dgllife.model import AttentiveFPPredictor # type: ignore
import argparse

##############################################
# 辅助函数：构建分子图的原子与键特征
##############################################
def get_atom_features(mol):
    """
    获取分子的原子特征（仿照 Attentive FP 的特征构建方式）。
    """
    atoms_features = []
    for atom in mol.GetAtoms():
        # 对原子符号进行 one-hot 编码，预定义顺序列表中未出现的视为“未知”
        atom_Symbol = list(np.array(atom.GetSymbol()) == np.array(
            ['B', 'C', 'N', 'O', 'F', 'Si', 'P', 'S', 'Cl', 'As', 'Se', 'Br', 'Te', 'I', 'At']
        ))
        # 若在预定义列表中找不到，则最后一个位置置 True
        atom_Symbol.append(False) if True in atom_Symbol else atom_Symbol.append(True)
        atom_Symbol = np.array(atom_Symbol).astype(np.float32)

        # 原子度 one-hot（假设最大度为5，共6维）
        atom_degree = (np.array(atom.GetDegree()) == np.array([i for i in range(6)])).astype(np.float32)
        # 形式电荷
        atom_FormalCharge = np.array([atom.GetFormalCharge()], dtype=np.float32)
        # 自由基电子数
        atom_RadicalElectrons = np.array([atom.GetNumRadicalElectrons()], dtype=np.float32)
        # 杂化类型 one-hot（SP, SP2, SP3, SP3D, SP3D2），未出现则标记为未知
        atom_Hybridization = list(atom.GetHybridization() == np.array(
            [Chem.rdchem.HybridizationType.SP, Chem.rdchem.HybridizationType.SP2,
             Chem.rdchem.HybridizationType.SP3, Chem.rdchem.HybridizationType.SP3D,
             Chem.rdchem.HybridizationType.SP3D2]
        ))
        atom_Hybridization.append(False) if True in atom_Hybridization else atom_Hybridization.append(True)
        atom_Hybridization = np.array(atom_Hybridization).astype(np.float32)
        
        # 是否芳香
        atom_IsAromatic = np.array([atom.GetIsAromatic()], dtype=np.float32)
        # 邻接氢原子数 one-hot（假设最大为4，共5维）
        atom_TotalNumHs = (np.array(atom.GetTotalNumHs()) == np.array([i for i in range(5)])).astype(np.float32)
        # 手性中心（布尔值）
        atom_chiral = np.array([atom.HasProp('_ChiralityPossible')], dtype=np.float32)
        # 手性类型（R/S），若无手性信息则均为 False，one-hot 长度2
        atom_chirality_type = np.array([False, False], dtype=np.float32) if not atom.HasProp('_CIPCode') \
                              else (np.array(atom.GetProp('_CIPCode')) == np.array(['R', 'S'])).astype(np.float32)
        
        # 添加原子量，归一化到 [0,1]（假设原子量范围为 0-80）
        atomic_mass = round(atom.GetMass())
        standardized_value = np.array([(atomic_mass - 0) / (80 - 0)], dtype=np.float32)
        
        # 拼接所有特征
        atom_features = np.concatenate([
            atom_Symbol, atom_degree, atom_FormalCharge, atom_RadicalElectrons,
            atom_Hybridization, atom_IsAromatic, atom_TotalNumHs, atom_chiral,
            atom_chirality_type, standardized_value
        ], axis=0)
        
        atoms_features.append(list(atom_features))
        
    return atoms_features

def get_bond_features(mol, G):
    """
    获取分子的键特征，并同时在图 G 中添加边。
    """
    bonds_features = []
    for bond in mol.GetBonds():
        start_idx = bond.GetBeginAtomIdx()
        end_idx = bond.GetEndAtomIdx()
        # 添加两条边（无向图的正反方向）
        G.add_edges(start_idx, end_idx)
        G.add_edges(end_idx, start_idx)  

        # ==== 特征构建 ====
        # 键类型 one-hot（4种类型）
        bond_type = list(bond.GetBondType() == np.array([Chem.rdchem.BondType.SINGLE,
                                                         Chem.rdchem.BondType.DOUBLE,
                                                         Chem.rdchem.BondType.TRIPLE,
                                                         Chem.rdchem.BondType.AROMATIC]))
        bond_type = np.array(bond_type).astype(np.int32)
        
        # 其他特征
        bond_IsConjugated = np.array([bond.GetIsConjugated()], dtype=np.int32)
        bond_IsInRing = np.array([bond.IsInRing()], dtype=np.int32)
        bond_Stereo = list(bond.GetStereo() == np.array([Chem.rdchem.BondStereo.STEREONONE,
                                                         Chem.rdchem.BondStereo.STEREOANY,
                                                         Chem.rdchem.BondStereo.STEREOZ,
                                                         Chem.rdchem.BondStereo.STEREOE]))
        bond_Stereo = np.array(bond_Stereo).astype(np.int32)
        
        # 拼接特征
        bond_features = np.concatenate([
            bond_type, 
            bond_IsConjugated, 
            bond_IsInRing, 
            bond_Stereo
        ], axis=0)
        
        # ==== 关键修复：每个bond为两条边添加完全相同的特征 ====
        bonds_features.append(bond_features)  # 第一条边
        bonds_features.append(bond_features)  # 第二条边
    
    return bonds_features


##############################################
# 数据集与 collate 函数（仅含类别型额外特征）
##############################################
class MyData(Dataset):
    def __init__(self, g_list, label_list, smiles_list, extra_cat_list):
        self.g_list = g_list
        # 回归任务标签转为 float 类型
        self.label_list = torch.tensor(label_list, dtype=torch.long)
        self.smiles = smiles_list
        self.extra_cat = extra_cat_list    # 每个元素为类别型特征的索引列表

    def __getitem__(self, idx):
        return self.smiles[idx], self.g_list[idx], self.label_list[idx], self.extra_cat[idx]

    def __len__(self):
        return len(self.label_list)

def collate_molgraphs(data):
    """
    每个样本返回 4 个元素：smiles, graph, label, extra_cat
    对图进行 batch，并将标签与额外特征转为 tensor。
    """
    if len(data[0]) == 4:
        smiles, graphs, labels, extra_cat = map(list, zip(*data))
        g = dgl.batch(graphs)
        g.set_n_initializer(dgl.init.zero_initializer)
        g.set_e_initializer(dgl.init.zero_initializer)
        labels = torch.stack(labels, dim=0)
        extra_cat = torch.tensor(extra_cat, dtype=torch.long)  # shape: (batch_size, num_cat_features)
        return smiles, g, labels, extra_cat
    elif len(data[0]) == 3:
        smiles, graphs, labels = map(list, zip(*data))
        g = dgl.batch(graphs)
        g.set_n_initializer(dgl.init.zero_initializer)
        g.set_e_initializer(dgl.init.zero_initializer)
        labels = torch.stack(labels, dim=0)
        return smiles, g, labels
    else:
        raise ValueError("数据元组长度不支持！")
    

# 参数解析部分
def parse_args():
    parser = argparse.ArgumentParser(description='AttentiveFP Classification')
    '''修改'''
    # 模型参数
    parser.add_argument('--graph_feat_size', type=int, default=256)
    parser.add_argument('--num_layers', type=int, default=3)
    parser.add_argument('--num_timesteps', type=int, default=5)
    parser.add_argument('--dropout', type=float, default=0.2)
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200)
    parser.add_argument('--batch_size', type=int, default=128)
    parser.add_argument('--lr', type=float, default=1e-3)
    parser.add_argument('--optimizer', choices=['Adam'], default='Adam') #'Adam', 'AdamW', 'SGD'
    parser.add_argument('--patience', type=int, default=20)
    parser.add_argument('--device', default='cuda:1')
    
    return parser.parse_args()


##############################################
# 模型：AttentiveFP 回归器，增加类别型额外特征嵌入模块
##############################################
class AttentiveFPClassifier(nn.Module):
    def __init__(self, args, cat_dims, cat_embed_dims, num_classes):
        super().__init__()
        # 使用固定参数构建AttentiveFP
        self.fp = AttentiveFPPredictor(
            node_feat_size=40,
            edge_feat_size=10,
            graph_feat_size=args.graph_feat_size,
            num_layers=args.num_layers,
            num_timesteps=args.num_timesteps,
            dropout=args.dropout,
            n_tasks=1
        )
        
        # 类别特征嵌入层
        if cat_dims:
            self.cat_embeddings = nn.ModuleList([
                nn.Embedding(v, d) for v, d in zip(cat_dims, cat_embed_dims)
            ])
            self.cat_dims_sum = sum(cat_embed_dims)
        else:
            self.cat_embeddings = None
            self.cat_dims_sum = 0
            
        # 最终回归层
        self.fc = nn.Sequential(
            nn.Linear(args.graph_feat_size + self.cat_dims_sum, 256),
            nn.ReLU(),
            nn.Dropout(args.dropout),
            nn.Linear(256, num_classes)  # 输出类别数
        )

    def forward(self, g, node_feats, edge_feats, cats):
        # AttentiveFP前向
        node_embs = self.fp.gnn(g, node_feats, edge_feats)
        graph_repr = self.fp.readout(g, node_embs)
        
        # 类别特征处理
        if self.cat_embeddings:
            embed_list = [emb(cats[:,i]) for i, emb in enumerate(self.cat_embeddings)]
            cat_repr = torch.cat(embed_list, dim=1)
        else:
            cat_repr = torch.zeros(graph_repr.size(0), 0).to(graph_repr.device)
            
        # 联合特征
        combined = torch.cat([graph_repr, cat_repr], dim=1)
        return self.fc(combined)

##############################################
# 训练和评估函数
##############################################
def train(model, loader, optimizer, criterion, device, args):
    model.train()
    total_loss = 0
    all_labels= []
    all_probs = []
    all_preds = []

    
    for batch in tqdm(loader, desc="Training"):
        smiles, g, y, cats = batch
        g = g.to(device)
        y = y.to(device)
        cats = cats.to(device)
        
        optimizer.zero_grad()
        logits = model(g, g.ndata["hv"], g.edata["he"], cats)
        loss = criterion(logits, y)
        loss.backward()
        optimizer.step()
        
        probs = torch.softmax(logits, dim=1).detach().cpu().numpy()
        preds = np.argmax(probs, axis=1)
        total_loss += loss.item()

        all_labels.extend(y.cpu().numpy())
        all_preds.extend(preds)
        all_probs.extend(probs[:,1])
    metrics = calculate_metrics(all_labels, all_preds, all_probs)
    return metrics, total_loss/len(loader)

def evaluate(model, loader, criterion, device, args):
    model.eval()
    total_loss = 0
    all_labels = [] 
    all_probs = []
    all_preds = []
    
    with torch.no_grad():
        for batch in tqdm(loader, desc="Evaluating"):
            smiles, g, y, cats = batch
            g = g.to(device)
            y = y.to(device)
            cats = cats.to(device)
            
            logits = model(g, g.ndata["hv"], g.edata["he"], cats)
            loss = criterion(logits, y)
            
            probs = torch.softmax(logits, dim=1).cpu().numpy()
            preds = np.argmax(probs, axis=1)
            
            total_loss += loss.item()
            all_labels.extend(y.cpu().numpy())
            all_preds.extend(preds)
            all_probs.extend(probs[:, 1])
    
    metrics = calculate_metrics(all_labels, all_preds, all_probs)
    return metrics, total_loss/len(loader)


from sklearn.metrics import (
    accuracy_score,
    f1_score,
    recall_score,
    precision_score,
    confusion_matrix,
    balanced_accuracy_score,
    roc_auc_score,
    precision_recall_curve,
    auc  # 关键导入
)

def calculate_metrics(labels, preds, probs):
    metrics = {}
    labels = np.array(labels)
    preds = np.array(preds)
    probs = np.array(probs)
    
    # ROC-AUC
    metrics['ROC-AUC'] = roc_auc_score(labels, probs)
    
    # PR-AUC（需处理全负样本情况）
    if len(np.unique(labels)) > 1:
        precision, recall, _ = precision_recall_curve(labels, probs)
        metrics['PR-AUC'] = auc(recall, precision)
    else:
        metrics['PR-AUC'] = 0.0
    
    # 基本指标
    metrics['Accuracy'] = accuracy_score(labels, preds)
    metrics['F1'] = f1_score(labels, preds)
    metrics['Balanced_Accuracy'] = balanced_accuracy_score(labels, preds)
    metrics['Precision']=precision_score(labels, preds)
    metrics['RTP'] = recall_score(labels, preds)
    cm = confusion_matrix(labels, preds, labels=[0, 1])
    tn, fp, fn, tp = cm.ravel()
    metrics['RFP'] = fp / (fp + tn) if (fp + tn) > 0 else 0.0

    return metrics

# 加载保存的最佳模型和模型参数
def load_saved_model(args, cat_dims, cat_embed_dims, model_path):
    """加载保存的最佳模型参数"""
    model = AttentiveFPClassifier(args, cat_dims, cat_embed_dims)
    model.load_state_dict(torch.load(model_path))
    return model

# 对整个数据集进行预测
def predict_on_full_data(model, dataset, batch_size, device):
        model.eval()
        all_probs = []
        all_labels = []
        
        dataloader = DataLoader(dataset, batch_size, collate_fn=collate_molgraphs)
        with torch.no_grad():
            for smiles, g, labels, cats in dataloader:
                g = g.to(device)
                cats = cats.to(device)
                logits = model(g, g.ndata["hv"], g.edata["he"], cats)
                probs = torch.softmax(logits, dim=1).cpu().numpy()
                all_probs.append(probs)
                all_labels.append(labels.numpy())
        
        all_probs = np.concatenate(all_probs)
        all_labels = np.concatenate(all_labels)
        all_preds = np.argmax(all_probs, axis=1)
        return all_preds, all_labels, all_probs

def main(args):
    # ==== 设备设置 ====
    device = torch.device(args.device)
    print(f"Using device: {device}")
    
    # ==== 数据加载与预处理 ====
    '''修改'''
    print("\n==== Data Loading ====")
    data = pd.read_excel("/home/<USER>/BXL/WH/genotoxicity/genemutation/471/AFP/471_clean_Split.xlsx")
    
    # 构建分子图数据集
    valid_indices = []
    g_list, label_list, smiles_list, extra_cat_list = [], [], [], []
    
    # 定义类别特征列和其他预处理参数
    '''修改'''
    '''没有特征列改为空列表'''
    cat_columns = ["Test_Organism", "Species_Group"] 
    chembl_id_col = "Canonical smiles"  # 假设保存SMILES的列名
    
    # 遍历每个分子
    for idx, row in tqdm(data.iterrows(), total=len(data), desc="Building Graphs"):
        mol = Chem.MolFromSmiles(row[chembl_id_col])
        if mol is None:  # 跳过无效分子
            print(f"Invalid SMILES in row {idx}: {row[chembl_id_col]}")
            continue
        valid_indices.append(idx)
        
        # 构建DGL图
        g = DGLGraph()
        
        # 添加原子节点
        atom_features = get_atom_features(mol)
        g.add_nodes(len(atom_features))
        g.ndata["hv"] = torch.tensor(atom_features, dtype=torch.float32)
        
        # 添加边和键特征
        bond_features = get_bond_features(mol, g)
        if len(bond_features) > 0:
            g.edata["he"] = torch.tensor(bond_features, dtype=torch.float32)
        else:  # 添加空特征保持维度
            g.edata["he"] = torch.zeros((0, 10), dtype=torch.float32)
            
        # 收集数据
        g_list.append(g)
        smiles_list.append(row[chembl_id_col])
        '''修改'''
        label_list.append(row["assay_result"])  # 目标列名 
        extra_cat_list.append([row[col] for col in cat_columns])
        
    # ==== 类别特征编码 ====
    print("\n==== Categorical Feature Encoding ====")
    from sklearn.preprocessing import LabelEncoder

    # 获取训练数据索引
    valid_data = data.loc[valid_indices].reset_index(drop=True)  # 有效数据（已过滤无效SMILES）
    train_indices = valid_data.index[valid_data["set"] == "train"].tolist()  # 训练集的索引

    print("\n==== 数据分布验证 ====")
    print(f"原始数据总量: {len(data)}")
    print(f"有效转化成功的样本数: {len(valid_indices)}")

    cat_encoders = {}
    encoded_cat = []

    for col_idx, col in enumerate(cat_columns):
        # 从训练集中收集所有可能的类别值
        train_values = [item[col_idx] for item in np.array(extra_cat_list)[train_indices]]
        unique_train_values = np.unique(train_values).tolist()  # 训练集中的唯一值
        
        # 为整个数据集（包括测试/验证）中的未知类别添加'Unknown'标签
        all_values = []
        for item in extra_cat_list:
            val = item[col_idx]
            # 检测是否在训练集的已知类别中
            if val not in unique_train_values:
                val = 'Unknown'  # 标记未知类别
            all_values.append(val)
        
        # 创建带有'Unknown'类别的LabelEncoder
        le = LabelEncoder()
        le.fit(unique_train_values + ['Unknown'])  # 训练集中可能的类别 + 未知标记
        
        # 转换处理后的数据（注意此时包含修正后的'Unknown'）
        encoded_values = le.transform(all_values)
        encoded_cat.append(encoded_values)
        cat_encoders[col] = le

    extra_cat_list = np.array(encoded_cat).T.tolist()

    
    # ==== 数据集划分 ====
    print("\n==== Dataset Splitting ====")

    valid_data = data.loc[valid_indices].reset_index(drop=True)

    train_mask = valid_data["set"] == "train"
    val_mask = valid_data["set"] == "valid"
    test_mask = valid_data["set"] == "test"
    
    train_set = MyData(
        [g_list[i] for i in np.where(train_mask)[0]],
        [label_list[i] for i in np.where(train_mask)[0]],
        [smiles_list[i] for i in np.where(train_mask)[0]],
        [extra_cat_list[i] for i in np.where(train_mask)[0]]
    )
    
    val_set = MyData(
        [g_list[i] for i in np.where(val_mask)[0]],
        [label_list[i] for i in np.where(val_mask)[0]],
        [smiles_list[i] for i in np.where(val_mask)[0]],
        [extra_cat_list[i] for i in np.where(val_mask)[0]]
    )
    
    test_set = MyData(
        [g_list[i] for i in np.where(test_mask)[0]],
        [label_list[i] for i in np.where(test_mask)[0]],
        [smiles_list[i] for i in np.where(test_mask)[0]],
        [extra_cat_list[i] for i in np.where(test_mask)[0]]
    )

    # ==== 类别特征维度处理 ====
    cat_dims = [len(le.classes_) for le in cat_encoders.values()] 
    cat_embed_dims = [max(1, min(50, dim//2)) for dim in cat_dims]
    
    # ==== 模型初始化 ====
    print(f"\n==== Model Initialization ====")
    num_classes = 2
    model = AttentiveFPClassifier(args, cat_dims, cat_embed_dims, num_classes).to(device)
    
    # ==== 训练配置 ====
    optimizer = getattr(torch.optim, args.optimizer)(model.parameters(), lr=args.lr)
    criterion = nn.CrossEntropyLoss()

    # ==== 数据装载器 ====
    train_loader = DataLoader(train_set, batch_size=args.batch_size, 
                            shuffle=True, collate_fn=collate_molgraphs)
    val_loader = DataLoader(val_set, batch_size=args.batch_size,
                          shuffle=False, collate_fn=collate_molgraphs)
    test_loader = DataLoader(test_set, batch_size=args.batch_size,
                           shuffle=False, collate_fn=collate_molgraphs)

    # ==== 训练循环 ====
    print("\n==== Training Started ====")
    best_val_f1 = 0.0
    no_improve = 0
    
    for epoch in range(args.epochs):
        train_metrics, train_loss = train(model, train_loader, optimizer, criterion, device, args)
        val_metrics, val_loss = evaluate(model, val_loader, criterion, device, args)
        
        # 打印训练和验证指标
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print(f"Train Loss: {train_loss:.4f} | " + 
              " | ".join([f"{k}: {v:.4f}" for k, v in train_metrics.items()]))
        print(f"Val Loss: {val_loss:.4f} | " + 
              " | ".join([f"{k}: {v:.4f}" for k, v in val_metrics.items()]))
        
        # 早停策略（根据F1）
        if val_metrics['F1'] > best_val_f1:
            best_val_f1 = val_metrics['F1']
            no_improve = 0
            '''修改'''
            torch.save(model.state_dict(), "/home/<USER>/BXL/WH/genotoxicity/genemutation/471/AFP/AFP_471.pth")
        else:
            no_improve +=1
            if no_improve >= args.patience:
                print("Early stopping triggered.")
                break
    
    # ==== 最终测试 ====
    print("\n==== Final Evaluation ====")
    model.load_state_dict(torch.load("/home/<USER>/BXL/WH/genotoxicity/genemutation/471/AFP/AFP_471.pth"))
    test_metrics, test_loss = evaluate(model, test_loader, criterion, device, args)
    print("\n=== Test Results ===")
    print(f"Loss: {test_loss:.4f} | " + " | ".join([f"{k}: {v:.4f}" for k, v in test_metrics.items()]))

    # ==== 全数据预测并保存 ====
    print("\n==== Full Data Prediction ====")
    full_dataset = MyData(g_list, label_list, smiles_list, extra_cat_list)

    preds, labels, probs = predict_on_full_data(model, full_dataset, args.batch_size, device)
    data['Predicted_Class'] = preds
    data['Probability_Class1'] = probs[:, 1]  # 假设类别1概率
    '''修改'''
    data.to_excel("/home/<USER>/BXL/WH/genotoxicity/genemutation/471/AFP/471_clean_Split_prediction.xlsx", index=False)
    print("Prediction results saved to final_predictions.xlsx")

if __name__ == "__main__":
    args = parse_args()
    main(args)
    
