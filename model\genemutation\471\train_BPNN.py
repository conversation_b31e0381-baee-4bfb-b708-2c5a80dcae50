
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torcheval.metrics import R2Score

from sklearn.metrics import root_mean_squared_error, mean_absolute_error, r2_score

from sklearn.model_selection import KFold


class EarlyStopping:
    def __init__(self, model_path, patience=10, min_delta=0, mode='max', start_from_epoch=0):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.start_from_epoch = start_from_epoch
        
        self.model_path = model_path
        self.counter = 0
        self.best_metric = float('inf')
        self.best_epoch = 0
    
    def __call__(self, updated_value, epoch, model):
        if self.mode == 'max':
            updated_value = -updated_value
        if updated_value < self.best_metric:
            self.best_metric = updated_value
            self.best_epoch = epoch
            self.counter = 0
            torch.save(model.state_dict(), self.model_path)
        elif updated_value >= (self.best_metric - self.min_delta):
            if epoch >= self.start_from_epoch:
                self.counter += 1
            if self.counter >= self.patience:
                return True
        return False


class GYX_Dataset(Dataset):
    def __init__(self, X, y):
        self.X = X
        self.y = y.reshape(-1, 1)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]
    

class BPNN(nn.Module):
    def __init__(self, input_dim, hidden_dim):
        super(BPNN, self).__init__()
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.relu = nn.ReLU()
        self.fc3 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.fc2 = nn.Linear(hidden_dim // 2, 1)

    def forward(self, inputs):
        out = self.fc1(inputs)
        out = self.relu(out)
        out = self.fc3(out)
        out = self.relu(out)
        out = self.fc2(out)
        return out


def load_dataset(endpoint):
    DATA_DIR = "./data/"
    data_filename = f"imputed_{endpoint}.csv"
    df = pd.read_csv(DATA_DIR + data_filename)

    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    target_label_map = {'HAA': 'HAAs',
                        'HAN': 'HANs',
                        'THM': 'T-THMs'}
    X = df[predictors].values
    y = df[target_label_map[endpoint]].values 
    return X, y


def train(model, dataloader, criterion, optimizer, device):
    model.train()
    total_mse_loss = 0.0
    total_examples = 0
    r2_metric = R2Score()

    for inputs, targets in dataloader:
        inputs = inputs.float().to(device)
        targets = targets.float().to(device)
        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()

        total_mse_loss += loss.item()
        r2_metric.update(outputs, targets)
        total_examples += targets.size(0)
    
    avg_mse_loss = total_mse_loss / total_examples
    r2_score = r2_metric.compute()

    return total_mse_loss, avg_mse_loss, r2_score.item()

    
def eval(model, dataloader, criterion, device):
    model.eval()
    total_mse_loss = 0.0
    total_examples = 0
    r2_metric = R2Score()
    true_targets = []
    pred_targets = []

    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs = inputs.float().to(device)
            targets = targets.float().to(device)

            outputs = model(inputs)
            loss = criterion(outputs, targets)
            total_mse_loss += loss.item()

            r2_metric.update(outputs, targets)
            total_examples += targets.size(0)
            
            true_targets.extend(targets.cpu().numpy())
            pred_targets.extend(outputs.cpu().numpy())

    avg_mse_loss = total_mse_loss / total_examples
    r2_score = r2_metric.compute()

    return total_mse_loss, avg_mse_loss, r2_score.item(), true_targets, pred_targets


def main(endpoint, random_seed, reg, emb_dim):

    reg = 0.01

    torch.manual_seed(random_seed)

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(device)

    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    X, y = load_dataset(endpoint)

    X_train_all, X_test, y_train_all, y_test = train_test_split(X, y, test_size=0.2, random_state=random_seed)
    X_train, X_val, y_train, y_val = train_test_split(X_train_all, y_train_all, test_size=0.2, random_state=random_seed)

    # Normalize Data
    scaler = StandardScaler().fit(X_train_all)
    X_train_all = scaler.transform(X_train_all)
    X_train = scaler.transform(X_train)
    X_val = scaler.transform(X_val)
    X_test = scaler.transform(X_test)

    train_dataset = GYX_Dataset(X_train, y_train)
    val_dataset = GYX_Dataset(X_val, y_val)
    test_dataset = GYX_Dataset(X_test, y_test)

    eval_train_val_dataset = GYX_Dataset(X_train_all, y_train_all)

    val_size = X_val.shape[0]
    test_size = X_test.shape[0]
    train_dataloader = DataLoader(train_dataset, batch_size=128, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=val_size, shuffle=False)
    test_dataloader = DataLoader(test_dataset, batch_size=test_size, shuffle=False)
    eval_train_val_dataloader = DataLoader(eval_train_val_dataset, batch_size=64, shuffle=False)

    # Build Model
    input_dim = len(predictors)
    hidden_dim = emb_dim
    model = BPNN(input_dim=input_dim, hidden_dim=hidden_dim).to(device)

    # Train settings
    lr = 1e-3
    num_epochs = 20000
    criterion = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=reg)
    model_path = f"./BPNN_checkpoints/{endpoint}/best_{hidden_dim}dim_{endpoint}_seed{random_seed}.pt"
    early_stopper = EarlyStopping(model_path=model_path,
                                  patience=500, mode='max', start_from_epoch=1000)

    for epoch in range(num_epochs):
        train_total_mse_loss, train_avg_mse_loss, train_r2 = train(model, train_dataloader, criterion, optimizer, device)
        val_total_mse_loss, val_avg_mse_loss, val_r2, val_targets, val_preds = eval(model, val_dataloader, criterion, device=device)

        if (epoch + 1) % 1000 == 0:
            print(f"Epoch {epoch+1}/{num_epochs}")
            print(f"\tTrain:")
            print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")
            print(f"\tValidation")
            print(f"\t\t val_total_loss: {val_total_mse_loss:.6f}, val_avg_loss: {val_avg_mse_loss:.6f}, val_r2: {val_r2:.6f}")
        
        if early_stopper(val_r2, epoch+1, model):
            model.load_state_dict(torch.load(model_path))
            print(f"Improvement is not observed for the past {early_stopper.patience} epochs. Early Stopping...")
            print(f"Loading Best Model Checkpoint from epoch {early_stopper.best_epoch} with validation r2 {-early_stopper.best_metric:.4f}")
            break


    print("Evaluation")   
    train_total_mse_loss, train_avg_mse_loss, train_r2, train_targets, train_preds = eval(model, eval_train_val_dataloader, criterion, device=device)
    print(f"\tTrain")
    print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")

    test_total_mse_loss, test_avg_mse_loss, test_r2, test_targets, test_preds = eval(model, test_dataloader, criterion, device=device)
    print(f"\tTest")
    print(f"\t\t test_total_loss: {test_total_mse_loss:.6f}, test_avg_loss: {test_avg_mse_loss:.6f}, test_r2: {test_r2:.6f}")

    
    torch.save(model, f"./results/models/{endpoint}_BPNN_seed{random_seed}_best.pt")

    scores = cross_validation(X_train_all, y_train_all, emb_dim, reg, cv=10)
    q2 = np.mean(scores)

    result_dict = {'endpoint': endpoint,
                   'seed': random_seed,
                   'l2_reg': reg,
                   'train_rmse': root_mean_squared_error(train_targets, train_preds),
                   'train_r2': train_r2,
                   'ext_rmse': root_mean_squared_error(test_targets, test_preds),
                   'ext_r2': test_r2,
                   'train_q2': q2,
                   'hidden_dim': hidden_dim,
                   'best_epoch': early_stopper.best_epoch}

    train_pred_dict = {'train_true': np.array(train_targets).squeeze(), 'train_pred': np.array(train_preds).squeeze()}
    pred_df = pd.DataFrame(train_pred_dict)
    pred_df.to_csv(f"./results/predictions/{endpoint}_BPNN_train_predictions.csv", index=False)

    ext_pred_dict = {'ext_true': np.array(test_targets).squeeze(), 'ext_pred': np.array(test_preds).squeeze()}
    pred_df = pd.DataFrame(ext_pred_dict)
    pred_df.to_csv(f"./results/predictions/{endpoint}_BPNN_ext_predictions.csv", index=False)

    result_df = pd.DataFrame([result_dict])
    result_df.to_csv(f"./results/scores/{endpoint}_BPNN_results.csv", index=False)

    return result_dict


def cross_validation(X, y, emb_dim, reg, cv):
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
    validation_scores = []

    kf = KFold(n_splits=cv)
    for i, (train_idx, test_idx) in enumerate(kf.split(X)):
        curr_X_train, curr_X_val, curr_y_train, curr_y_val = X[train_idx], X[test_idx], y[train_idx], y[test_idx]
        val_size = curr_X_val.shape[0]

        train_dataset = GYX_Dataset(curr_X_train, curr_y_train)
        val_dataset = GYX_Dataset(curr_X_val, curr_y_val)

        train_dataloader = DataLoader(train_dataset, batch_size=128, shuffle=True)
        val_dataloader = DataLoader(val_dataset, batch_size=val_size, shuffle=False)

        # Build Model
        input_dim = len(predictors)
        hidden_dim = emb_dim
        model = BPNN(input_dim=input_dim, hidden_dim=hidden_dim).to(device)

        # Train settings
        lr = 1e-3
        num_epochs = 20000
        criterion = nn.MSELoss()
        optimizer = optim.SGD(model.parameters(), lr=lr, weight_decay=reg)
        model_path = f"./BPNN_checkpoints/{endpoint}/best_{hidden_dim}dim_{endpoint}_cv_fold{i}.pt"
        early_stopper = EarlyStopping(model_path=model_path,
                                        patience=1000, mode='max', start_from_epoch=1000)

        for epoch in range(num_epochs):
            train_total_mse_loss, train_avg_mse_loss, train_r2 = train(model, train_dataloader, criterion, optimizer, device)
            val_total_mse_loss, val_avg_mse_loss, val_r2, val_targets, val_preds = eval(model, val_dataloader, criterion, device=device)
            
            if (epoch + 1) % 1000 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}")
                print(f"\tTrain:")
                print(f"\t\t train_total_loss: {train_total_mse_loss:.6f}, train_avg_loss: {train_avg_mse_loss:.6f}, train_r2: {train_r2:.6f}")
                print(f"\tValidation")
                print(f"\t\t val_total_loss: {val_total_mse_loss:.6f}, val_avg_loss: {val_avg_mse_loss:.6f}, val_r2: {val_r2:.6f}")
            
            if early_stopper(val_r2, epoch+1, model):
                model.load_state_dict(torch.load(model_path))
                print(f"Improvement is not observed for the past {early_stopper.patience} epochs. Early Stopping...")
                print(f"Loading Best Model Checkpoint from epoch {early_stopper.best_epoch} with validation r2 {-early_stopper.best_metric:.4f}")
                break

        model.load_state_dict(torch.load(model_path))

        print("Evaluation")   
        test_total_mse_loss, test_avg_mse_loss, test_r2, test_targets, test_preds = eval(model, val_dataloader, criterion, device=device)
        print(f"\tTest")
        print(f"\t\t test_total_loss: {test_total_mse_loss:.6f}, test_avg_loss: {test_avg_mse_loss:.6f}, test_r2: {test_r2:.6f}")

        validation_scores.append(test_r2)

    return validation_scores

if __name__ == '__main__':


    endpoint = 'HAA'    # or 'HAN' or 'THM'

    random_seed = 2 if endpoint == 'HAA' else 60 if endpoint == 'HAN' else 0

    main(endpoint, random_seed, reg=1e-2, emb_dim=64)

