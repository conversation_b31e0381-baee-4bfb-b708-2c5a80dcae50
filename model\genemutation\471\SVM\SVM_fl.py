import pandas as pd
import numpy as np
import ast
from sklearn.svm import SVC
from sklearn.metrics import (
    accuracy_score, balanced_accuracy_score, 
    precision_score, recall_score, f1_score,
    confusion_matrix, classification_report,
    roc_auc_score, average_precision_score,
    precision_recall_curve, auc
)
from sklearn.model_selection import GridSearchCV
from sklearn.preprocessing import OneHotEncoder, StandardScaler

def preprocess_train_data(data, fingerprint_column, feature_columns):
    data = data.copy()  # 确保对副本进行操作，避免 SettingWithCopyWarning
    # 预处理训练数据
    if fingerprint_column:
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
        max_length = data[fingerprint_column].apply(len).max()
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(lambda x: x + [0] * (max_length - len(x)))
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))
        max_length = 0

    numerical_features = []
    if 'Duration_Time' in feature_columns:
        numerical_features.append('Duration_Time')

    X_num = data[numerical_features].values if numerical_features else np.empty((len(data), 0))

    categorical_features = []
    if 'organism' in feature_columns:
        categorical_features.append('organism')
    if 'Media_Type' in feature_columns:
        categorical_features.append('Media_Type')
    if 'effect' in feature_columns:
        categorical_features.append('effect')

    if categorical_features:
        ohe = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
        X_cat = ohe.fit_transform(data[categorical_features])
    else:
        X_cat = np.empty((len(data), 0))
        ohe = None

    X = np.hstack([X_fp, X_num, X_cat])

    return X, ohe, max_length

def preprocess_test_data(data, fingerprint_column, feature_columns, ohe, max_length):
    data = data.copy()  # 确保对副本进行操作，避免 SettingWithCopyWarning
    if fingerprint_column:
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(lambda x: x + [0] * (max_length - len(x)))

        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))

    numerical_features = []
    if 'Duration_Time' in feature_columns:
        numerical_features.append('Duration_Time')

    X_num = data[numerical_features].values if numerical_features else np.empty((len(data), 0))

    categorical_features = []
    if 'organism' in feature_columns:
        categorical_features.append('organism')
    if 'Media_Type' in feature_columns:
        categorical_features.append('Media_Type')
    if 'effect' in feature_columns:
        categorical_features.append('effect')

    if categorical_features and ohe is not None:
        X_cat = ohe.transform(data[categorical_features])
    else:
        X_cat = np.empty((len(data), 0))

    X = np.hstack([X_fp, X_num, X_cat])

    return X

# ==================== 数据读取及预处理 ====================

# 读取数据文件，要求文件中必须包含 "set" 列，其取值为 "train"、"test" 和 "valid"
data_file_path = r"C:\Users\<USER>\OneDrive\Desktop\wanhua\数据\tjp\兔经皮分类clean_split_fp2.0.xlsx"  # 请根据实际路径修改
data = pd.read_excel(data_file_path)

# 定义特征和标签列
fingerprint_column = 'Morgan'         # 指定要使用的分子指纹列（如 Modred, ECFP, MACCS, Morgan, CMorgan 等）
feature_columns = ['', '']  # 除指纹外使用的其他特征（如数值特征 Duration_Value 也可加入）
label_column = 'label'         # 回归目标

# 根据 "set" 列划分数据集（注意：验证集列取值为 "valid"）
train_data = data[data['set'] == 'train']
val_data   = data[data['set'] == 'valid']
test_data  = data[data['set'] == 'test']

# 预处理训练数据
X_train, ohe, max_length = preprocess_train_data(train_data, fingerprint_column, feature_columns)
y_train = train_data[label_column].values.astype(int)

 # ========== 不平衡样本：对训练集进行SMOTE过采样 ==========
print("\n过采样前类别分布:", np.unique(y_train, return_counts=True))

# 定义过采样策略
# from imblearn.over_sampling import SMOTE
# smote = SMOTE(
#     sampling_strategy={1: 3000},  # 指定将类别1采样到X个样本
#     random_state=42,
#     k_neighbors=5  # 根据实际数据量调整，小样本数据集可减小该值
# )
# X_train_res, y_train_res = smote.fit_resample(X_train, y_train)

# 定义欠采样策略
from imblearn.under_sampling import RandomUnderSampler
under_sampler = RandomUnderSampler(
    sampling_strategy={0:504},  ### 控制各多数类样本量(改)
    random_state=42
)
X_train_res, y_train_res = under_sampler.fit_resample(X_train, y_train)

print("采样后类别分布:", np.unique(y_train_res, return_counts=True))

X_val = preprocess_test_data(val_data, fingerprint_column, feature_columns, ohe, max_length)
y_val = val_data[label_column].values.astype(int)
X_test = preprocess_test_data(test_data, fingerprint_column, feature_columns, ohe, max_length)
y_test = test_data[label_column].values.astype(int)
# 特征标准化
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)
# ==================== 模型训练与调优 ====================
svc = SVC(probability=True, random_state=42)  # 启用概率预测

# 定义参数网格
param_grid = {
    'C': [0.1, 1, 10],
    'gamma': ['scale', 'auto'],
    'kernel': ['rbf', 'linear',  'poly'],
    'class_weight': ['balanced']
}

# 根据类别数量选择评估指标
n_classes = len(np.unique(y_train))
scoring = 'roc_auc_ovr' if n_classes > 2 else 'roc_auc'
grid_search = GridSearchCV(
    svc, param_grid, cv=5, n_jobs=-1, verbose=2, 
    scoring=scoring
)
grid_search.fit(X_train_scaled, y_train)


# 输出最佳参数
print("最优参数:", grid_search.best_params_)

# 获取最佳模型
best_model = grid_search.best_estimator_

# ==================== 模型评估 ====================
# ==================== 模型评估函数 ====================
def evaluate_classification(y_true, y_pred, y_proba=None, set_name="Dataset"):
    print(f"\n{set_name}评估报告:")
    print(classification_report(y_true, y_pred))
    print("混淆矩阵:")
    print(confusion_matrix(y_true, y_pred))
    
    # 基础指标
    metrics = {
        '准确率': accuracy_score(y_true, y_pred),
        '平衡准确率': balanced_accuracy_score(y_true, y_pred),
        '加权F1': f1_score(y_true, y_pred, average='weighted'),
        '召回率': recall_score(y_true, y_pred, average='weighted'),
        '精确率': precision_score(y_true, y_pred, average='weighted')
    }
    
    # 二分类特定指标
    if n_classes == 2:
        metrics['AUC-ROC'] = roc_auc_score(y_true, y_proba[:, 1])
        # 计算 AUC-PR
        precision, recall, _ = precision_recall_curve(y_true, y_proba[:, 1])
        auc_pr = auc(recall, precision)
        metrics['AUC-PR'] = auc_pr

    else:  # 多分类
        try:
            metrics['AUC-ROC (OvR)'] = roc_auc_score(y_true, y_proba, multi_class='ovr')
        except:
            pass
    
    for name, value in metrics.items():
        print(f"{name}: {value:.4f}")
# 执行评估
for X, y, name in [(X_train_scaled, y_train, "训练集"), 
                   (X_val_scaled, y_val, "验证集"), 
                   (X_test_scaled, y_test, "测试集")]:
    y_pred = best_model.predict(X)
    y_proba = best_model.predict_proba(X) if n_classes >=2 else None
    evaluate_classification(y, y_pred, y_proba, name)

# ==================== 对整个数据集进行预测并输出结果 ====================

# 对原始数据（所有记录）进行预处理
X_all = preprocess_test_data(data, fingerprint_column, feature_columns, ohe, max_length)
X_all_scaled = scaler.transform(X_all)
y_all_pred = best_model.predict(X_all_scaled)
y_all_proba = best_model.predict_proba(X_all_scaled)

# 将预测结果添加到原始数据中，列名设为 "prediction"
data['prediction'] = y_all_pred
for i in range(y_all_proba.shape[1]):
    data[f'prob_class_{i}'] = y_all_proba[:, i]

# 输出带预测结果的 Excel 文件（包含原始数据、set 列及预测结果）
output_excel_file = r"C:\Users\<USER>\OneDrive\Desktop\wanhua\数据\tjp\兔经皮分类clean_split_fp2.0_SVM_Morgan.xlsx"  # 请根据实际情况修改路径
data.to_excel(output_excel_file, index=False)
print(f"带预测结果的文件已保存至: {output_excel_file}")
