# -*- coding: utf-8 -*-
"""
Created on Thu Oct 26 10:29:41 2023

@author: <PERSON>
"""
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.callbacks import EarlyStopping
import shap
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.layers import Activation

# 导入训练集和测试集数据
datatra = pd.read_csv('C:\\Users\\<USER>\\Desktop\\imputed_HAN.csv')
ylabel = ['HANs']
predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
X = datatra[predictors]
y = datatra[ylabel]

# 划分数据集为训练集和测试集
X_1, X_ext, y_1, y_ext = train_test_split(X, y, test_size=0.2, random_state=32)
X_train, X_test, y_train, y_test = train_test_split(X_1, y_1, test_size=0.2, random_state=16)

# 设置神经网络的参数
input_size = X_train.shape[1]
hidden_size = 5
epochs = 1000
batch_size = 256

# 创建并训练神经网络模型
model = Sequential()
model.add(Dense(hidden_size, input_dim=input_size, activation='sigmoid'))
model.add(Dense(1))  # 输出层
model.compile(loss='mean_squared_error', optimizer='adam')

# 定义提前停止的回调函数
early_stopping = EarlyStopping(monitor='val_loss', patience=5, mode='min')

# 手动交叉验证并计算平均Q2
cv_scores = []
for _ in range(10):
    model.fit(X_train, y_train, epochs=epochs, batch_size=batch_size, 
              validation_data=(X_test, y_test), 
              callbacks=[early_stopping])
    y_pred = model.predict(X_train)
    cv_scores.append(r2_score(y_train, y_pred))

average_q2 = np.mean(cv_scores)

# 训练模型
history = model.fit(X_train, y_train, epochs=epochs, batch_size=batch_size, 
                    validation_data=(X_test, y_test), 
                    callbacks=[early_stopping])

# 在测试集上进行预测
y_train_pred = model.predict(X_train)
y_pred = model.predict(X_test)

# 计算决定系数
r2_train = r2_score(y_train, y_train_pred)
r2_val = r2_score(y_test, y_pred)
print("交叉验证系数 (Q²_cv):", average_q2)
print("R²_train:", r2_train)
print("R²_test:", r2_val)

# 计算均方根误差（RMSE）
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
print("均方误差 (MSE):", mean_squared_error(y_test, y_pred))
print("均方根误差（RMSE）：", rmse)

# 对外部验证集进行预测
y_ext_pred = model.predict(X_ext)
r2_ext = r2_score(y_ext, y_ext_pred)
rmse1 = np.sqrt(mean_squared_error(y_ext, y_ext_pred))
print("R²_ext:", r2_ext)
print("均方根误差（RMSE1）：", rmse1)

df_train_predictions = pd.DataFrame(y_train_pred, columns=['HAN_predicted_train'])
df_test_predictions = pd.DataFrame(y_pred, columns=['HAN_predicted_test'])
df_ext_predictions = pd.DataFrame(y_ext_pred, columns=['HAN_predicted_ext'])
df_train_results = pd.concat([df_train_predictions, y_train.reset_index(drop=True)], axis=1)
df_test_results = pd.concat([df_test_predictions, y_test.reset_index(drop=True)], axis=1)
df_ext_results = pd.concat([df_ext_predictions, y_ext.reset_index(drop=True)], axis=1)
df_train_results.to_excel('C:\\Users\<USER>\\Desktop\\train_resultsHAN.xlsx', index=False)
df_test_results.to_excel('C:\\Users\<USER>\\Desktop\\test_resultsHAN.xlsx', index=False)
df_ext_results.to_excel('C:\\Users\<USER>\\Desktop\\ext_resultsHAN.xlsx', index=False)

# 创建SHAP解释器并计算SHAP值
#X_all = np.concatenate((X_train, X_test))
#background_data = shap.sample(X_all, 100)  # 选择100个样本作为背景
#explainer = shap.KernelExplainer(model.predict, background_data)
#shap_values_all = explainer.shap_values(X_all)

#shap_values_mean = np.mean(shap_values_all, axis=0)

# 计算SHAP值
#explainer = shap.KernelExplainer(model.predict, X_train)
#shap_values = explainer.shap_values(X_test)

# 创建SHAP值数据框
#shap_df = pd.DataFrame(shap_values, columns=predictors)

# 绘制SHAP摘要图
#plt.figure(figsize=(10, 8))
#shap.summary_plot(shap_values, X_test, feature_names=predictors, plot_type="bar", color='blue')

# 保存图像
#desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
#shap_plot_path = os.path.join(desktop_path, 'shap_summary_plot.png')
#plt.savefig(shap_plot_path, dpi=300, bbox_inches='tight')
#plt.close()

# 绘制小提琴图

#shap.summary_plot(shap_values, X_test, feature_names=predictors, plot_type="violin", color='blue')

# 保存小提琴图
#violin_plot_path = os.path.join(desktop_path, 'shap_violin_plot.png')
#plt.savefig(violin_plot_path, dpi=300, bbox_inches='tight')
#plt.close()
# 可选：将预测结果保存为CSV文件
#df_train_predictions = pd.DataFrame(y_train_pred, columns=['THM_predicted_train'])
#df_test_predictions = pd.DataFrame(y_pred, columns=['THM_predicted_test'])
#df_ext_predictions = pd.DataFrame(y_ext_pred, columns=['THM_predicted_ext'])
# 输出模型在外部验证数据上的预测结果
#df_train_predictions = pd.DataFrame(y_train_pred, columns=['THM_predicted_train'])
#df_test_predictions = pd.DataFrame(y_pred, columns=['THM_predicted_test'])
#df_ext_predictions = pd.DataFrame(y_ext_pred, columns=['THM_predicted_ext'])

# 将预测结果和真实值合并为一个DataFrame
#df_train_results = pd.concat([df_train_predictions, y_train.reset_index(drop=True)], axis=1)
#df_test_results = pd.concat([df_test_predictions, y_test.reset_index(drop=True)], axis=1)
#df_ext_results = pd.concat([df_ext_predictions, y_ext.reset_index(drop=True)], axis=1)

# 保存结果到Excel文件
#df_train_results.to_excel('C:\\Users\<USER>\\Desktop\\train_results.xlsx', index=False)
#df_test_results.to_excel('C:\\Users\<USER>\\Desktop\\test_results.xlsx', index=False)
#df_ext_results.to_excel('C:\\Users\<USER>\\Desktop\\ext_results.xlsx', index=False)
