# -*- coding: utf-8 -*-
"""
Created on Tue Nov 21 16:59:35 2023

@author: <PERSON>
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.base import BaseEstimator, RegressorMixin

class RBF_ANN(BaseEstimator, RegressorMixin):
    def __init__(self, num_rbf_neurons=10):
        self.num_rbf_neurons = num_rbf_neurons
        self.rbfs = None
        self.w = None

    def rbf(self, x, c, s):
        return np.exp(-1 / (2 * s**2) * np.linalg.norm(x - c)**2)

    def fit(self, X, y):
       N, D = X.shape
       self.rbfs = X[np.random.choice(N, self.num_rbf_neurons), :]
       Z = np.array([self.rbf(X, c, 1) for c in self.rbfs])
       Z = Z.T
       # 将y变形为二维数组
       y = y.reshape(-1, 1)
       self.w = np.linalg.lstsq(Z, y, rcond=None)[0]
    def predict(self, X):
        Z = np.array([self.rbf(X, c, 1) for c in self.rbfs])
        return Z.T.dot(self.w)

    def get_params(self, deep=True):
        return {'num_rbf_neurons': self.num_rbf_neurons}

    def set_params(self, **parameters):
        for parameter, value in parameters.items():
            setattr(self, parameter, value)
        return self


# 导入训练集和测试集数据
datatra = pd.read_csv('C:\\Users\\<USER>\\Desktop\\imputed_THMs.csv')
ylabel = 'T-THMs'
predictors = ['pH', 'NH4', 'Br', 'UV254', 'temp', 'Residue chlorine', 'DOC', 'Time']
X = datatra[predictors]
y = datatra[ylabel]

# 划分数据集为训练集、验证集和测试集
X_train, X_ext, y_train, y_ext = train_test_split(X, y, test_size=0.2, random_state=16)
X_train, X_test, y_train, y_test = train_test_split(X_train, y_train, test_size=0.2, random_state=16)

# 创建RBF-ANN模型
rbf_ann_model = RBF_ANN(num_rbf_neurons=10)

# 使用交叉验证来评估模型性能
cv = KFold(n_splits=10, shuffle=True, random_state=42)
cv_scores = cross_val_score(rbf_ann_model, X_train, y_train, cv=cv, scoring='r2')
average_q2 = np.mean(cv_scores)
print("cv_scores:", cv_scores)
print("Q²_cv:", average_q2)

# 模型训练
rbf_ann_model.fit(X_train.values, y_train.values)

# 模型预测
y_pred = rbf_ann_model.predict(X_test.values)
y_train_pred = rbf_ann_model.predict(X_train.values)

# 训练集决定系数
r2_train = r2_score(y_train, y_train_pred)
print("R²_train:", r2_train)

# 验证集决定系数
r2_val = r2_score(y_test, y_pred)
print("R²_test:", r2_val)

# 计算均方根误差（RMSE）
rmse = np.sqrt(mean_squared_error(y_test, y_pred))
mae = mean_absolute_error(y_test, y_pred)
print("均方根误差（RMSE）：", rmse)
print("平均绝对误差（MAE）：", mae)

# 在测试集上进行预测
y_ext_pred = rbf_ann_model.predict(X_ext.values)

# 计算外部验证决定系数
r2_ext = r2_score(y_ext, y_ext_pred)
print("R²_ext:", r2_ext)

rmse1 = np.sqrt(mean_squared_error(y_ext, y_ext_pred))
print("均方根误差（RMSE1）：", rmse1)

# 输出模型在外部验证数据上的预测结果
df_train_predictions = pd.DataFrame(y_train_pred, columns=['THM_predicted_train'])
df_test_predictions = pd.DataFrame(y_pred, columns=['THM_predicted_test'])
df_ext_predictions = pd.DataFrame(y_ext_pred, columns=['THM_predicted_ext'])

# 将预测结果和真实值合并为一个 DataFrame
df_train_results = pd.concat([df_train_predictions, y_train.reset_index(drop=True)], axis=1)
df_test_results = pd.concat([df_test_predictions, y_test.reset_index(drop=True)], axis=1)
df_ext_results = pd.concat([df_ext_predictions, y_ext.reset_index(drop=True)], axis=1)

# 保存结果到 Excel 文件
df_train_results.to_excel('C:\\Users\\<USER>\\Desktop\\train_resultsRBF,.xlsx', index=False)
df_test_results.to_excel('C:\\Users\\<USER>\\Desktop\\test_resultsRBF.xlsx', index=False)
df_ext_results.to_excel('C:\\Users\\<USER>\\Desktop\\ext_resultsRBF.xlsx', index=False)